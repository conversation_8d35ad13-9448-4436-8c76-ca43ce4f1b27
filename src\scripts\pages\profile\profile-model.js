import { updateData, uploadFile, getData } from '../../data/api.js';
import { saveSetupData, getAllSetupData } from '../../utils/indexeddb.js';
import authService from '../../data/auth-service.js';
import CONFIG from '../../config.js';

const ProfileModel = {
  async getUserProfile() {
    const setupData = await getAllSetupData();
    const userData = authService.getUserData();

    // Try to get avatar from API if user is authenticated
    let avatar = null;
    try {
      if (userData && authService.isAuthenticated()) {
        const accountData = await getData(CONFIG.API_ENDPOINTS.ACCOUNT.GET, true);
        avatar = accountData?.avatar || accountData?.data?.avatar || null;
      }
    } catch (error) {
      console.warn('Failed to fetch avatar from API:', error);
      // Continue with null avatar - will show default
    }

    return {
      avatar: avatar,
      fullName: setupData[0]?.name || '',
      username: userData?.username || '',
      experience: setupData[0]?.experience || '',
    };
  },

  getDefaultAvatarUrl() {
    return 'images/avatar.jpg';
  },

  async uploadProfilePicture(file) {
    const userData = authService.getUserData();

    if (!userData) {
      throw new Error('User belum login');
    }

    // Validate file
    if (!file) {
      throw new Error('File tidak ditemukan');
    }

    const maxSize = 2 * 1024 * 1024; // 2MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];

    if (file.size > maxSize) {
      throw new Error('Ukuran file maksimal 2MB');
    }

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Format file harus berupa gambar (JPEG, PNG, atau GIF)');
    }

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('avatar', file);

    try {
      // Upload to API
      const response = await uploadFile(CONFIG.API_ENDPOINTS.ACCOUNT.UPLOAD_AVATAR, formData, true);

      // Update user data in localStorage with new avatar URL
      const updatedUserData = {
        ...userData,
        avatar: response.avatar || response.data?.avatar || response.avatarUrl
      };

      authService.saveAuthData({
        token: authService.getToken(),
        refreshToken: authService.getRefreshToken(),
        user: updatedUserData,
      });

      return response;
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      throw error;
    }
  },

async updateUsername(newUsername) {
  const userData = authService.getUserData();

  if (!userData) {
    throw new Error('User belum login');
  }

  if (newUsername === userData.username) {
    // Username tidak berubah, skip update API
    return;
  }

  // Panggil API PUT /api/account untuk update username
  const updatedUser = await updateData('/api/account', {
    username: newUsername,
    email: userData.email,  // asumsi masih wajib dikirim
  });

  // Ambil token dan refreshToken yang sudah ada
  const token = authService.getToken();
  const refreshToken = authService.getRefreshToken();

  // Simpan kembali data lengkap agar tidak hilang
  authService.saveAuthData({
    token,
    refreshToken,
    user: {
      ...userData,
      username: updatedUser.username,
    },
  });
},


  async saveSetupData({ name, experience }) {
    // Ambil data setup lama dulu, jika ada
    const setupDataArr = await getAllSetupData();

    let id = setupDataArr[0]?.id;

    // Kalau belum ada data, buat baru
    const dataToSave = {
      id, // kalau undefined, IndexedDB auto increment
      name,
      experience,
    };

    await saveSetupData(dataToSave);
  },
};

export default ProfileModel;
