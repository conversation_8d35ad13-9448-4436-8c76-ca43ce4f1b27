import ProfilePresenter from "./profile-presenter";
import ProfileModel from "./profile-model";
import authService from "../../data/auth-service.js";
import { NavigationBar } from "../../components/NavigationBar.js";

export default class ProfilePage {
  async render() {
    // Get user display info from API or localStorage
    const userDisplayInfo = await this.getUserDisplayInfo();

    const navbar = new NavigationBar({
      currentPath: window.location.hash.slice(1),
      userInitial: userDisplayInfo.initial,
      showProfile: true,
    });

    return `
      <div class="profile-page-container">
        ${navbar.render()}

        <div class="profile-container">
          <aside class="profile-sidebar">
            <div class="sidebar-avatar">
              <img src="${ProfileModel.getDefaultAvatarUrl()}" alt="Avatar" class="avatar" id="sidebarAvatar"/>
              <p id="sidebarUsername">Username User</p>
              <p id="sidebarExperience">Experience Level</p>
              <button class="sidebar-button">Profile Pengguna</button>
            </div>
          </aside>

          <main>
            <section class="container-profile">
              <h1>Profile Pengguna</h1>
              <hr />
              <div class="profile-avatar-wrapper">
                <div class="avatar-left">
                  <img src="${ProfileModel.getDefaultAvatarUrl()}" alt="Avatar" id="avatarPreview" class="avatar"/>
                </div>
                <div class="edit-right">
                  <button id="editAvatarBtn">Edit Photo</button>
                  <p class="avatar-note">Format harus berupa gambar. Tidak boleh lebih dari 2MB.</p>
                </div>
              </div>

              <div class="profile-info">
                <div class="input-group">
                  <label for="fullNameInput">Nama Lengkap:</label>
                  <input type="text" id="fullNameInput" placeholder="Enter your full name" />
                </div>

                <div class="input-group">
                  <label for="usernameInput">Username:</label>
                  <input type="text" id="usernameInput" placeholder="Enter your username" />
                </div>
              </div>

              <div class="profile-experience">
                <label>Select your farming experience level:</label>
                <div class="experience-options">
                  <label><input type="radio" name="experience" value="Beginner" /> Beginner</label>
                  <label><input type="radio" name="experience" value="Intermediate" /> Intermediate</label>
                  <label><input type="radio" name="experience" value="Experienced" /> Experienced</label>
                </div>
              </div>

              <input type="file" id="avatarInput" accept="image/*" capture="environment" style="display:none"/>

              <div class="profile-save-btn-wrapper" style="margin-top: 20px;">
                <button id="saveProfileBtn" class="btn btn-primary">Edit Profile</button>
              </div>
            </section>
          </main>
        </div>

        <footer class="profile-footer">
          <p>&copy; 2025 AgriEdu. All rights reserved.</p>
        </footer>
      </div>
    `;
  }

  async afterRender() {
    // Set up navigation bar events
    this.setupNavigationEvents();

    // Initialize ProfilePresenter first
    await ProfilePresenter.init(this);

    // Set up avatar upload functionality with API integration
    this.setupAvatarUpload();
  }

  setupAvatarUpload() {
    const editBtn = document.getElementById("editAvatarBtn");
    const avatarInput = document.getElementById("avatarInput");
    const avatarPreview = document.getElementById("avatarPreview");

    if (editBtn && avatarInput && avatarPreview) {
      editBtn.addEventListener("click", () => {
        avatarInput.click();
      });

      avatarInput.addEventListener("change", async () => {
        const file = avatarInput.files[0];
        if (file) {
          try {
            // Validate file size and type
            const maxSize = 2 * 1024 * 1024; // 2MB
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];

            if (file.size > maxSize) {
              alert('Ukuran file maksimal 2MB');
              avatarInput.value = '';
              return;
            }

            if (!allowedTypes.includes(file.type)) {
              alert('Format file harus berupa gambar (JPEG, PNG, atau GIF)');
              avatarInput.value = '';
              return;
            }

            // Show preview immediately
            const reader = new FileReader();
            reader.onload = function (e) {
              avatarPreview.src = e.target.result;
              // Also update sidebar avatar
              const sidebarAvatar = document.getElementById("sidebarAvatar");
              if (sidebarAvatar) {
                sidebarAvatar.src = e.target.result;
              }
            };
            reader.readAsDataURL(file);

            // Upload to API immediately
            try {
              console.log('Uploading avatar to API...');
              await ProfilePresenter.handleAvatarUpload(file);
              console.log('Avatar uploaded successfully');

              // Show success message
              this.showMessage('Foto profil berhasil diupload!', 'success');
            } catch (uploadError) {
              console.error('Avatar upload failed:', uploadError);

              // Revert to previous avatar on upload failure
              const profile = await ProfileModel.getUserProfile();
              const avatarUrl = profile.avatar || ProfileModel.getDefaultAvatarUrl();
              avatarPreview.src = avatarUrl;
              const sidebarAvatar = document.getElementById("sidebarAvatar");
              if (sidebarAvatar) {
                sidebarAvatar.src = avatarUrl;
              }

              this.showMessage('Gagal mengupload foto profil: ' + uploadError.message, 'error');
              avatarInput.value = '';
            }
          } catch (error) {
            console.error('Error handling avatar upload:', error);
            this.showMessage('Terjadi kesalahan saat memproses file', 'error');
            avatarInput.value = '';
          }
        }
      });
    }
  }

  async getUserDisplayInfo() {
    try {
      // Try to get current user data from API first
      const userData = authService.getUserData();
      if (userData && userData.username) {
        const displayName = userData.username;
        return {
          name: displayName,
          initial: displayName.charAt(0).toUpperCase()
        };
      }
    } catch (error) {
      console.warn('Failed to get user data from API:', error);
    }

    // Fallback to localStorage
    const userName = localStorage.getItem("user_name") || "User";
    return {
      name: userName,
      initial: userName.charAt(0).toUpperCase()
    };
  }

  async setupNavigationEvents() {
    // Set up navigation bar events using the NavigationBar component's centralized event handling
    const userDisplayInfo = await this.getUserDisplayInfo();

    const navbar = new NavigationBar({
      currentPath: window.location.hash.slice(1),
      userInitial: userDisplayInfo.initial,
      showProfile: true,
    });

    // Use the NavigationBar's built-in event binding
    navbar.bindEvents();
  }

  async refreshNavigation() {
    // Method to refresh navigation bar when user data changes
    await this.setupNavigationEvents();
  }

  showProfile({ avatar, fullName, username, experience }) {
    // Update main profile form
    const fullNameInput = document.getElementById("fullNameInput");
    const usernameInput = document.getElementById("usernameInput");

    if (fullNameInput) fullNameInput.value = fullName || "";
    if (usernameInput) usernameInput.value = username || "";

    // Update experience radio buttons
    if (experience) {
      const radio = document.querySelector(
        `input[name="experience"][value="${experience}"]`
      );
      if (radio) radio.checked = true;
    }

    // Update avatar images with proper fallback logic
    const avatarUrl = avatar || ProfileModel.getDefaultAvatarUrl();
    const avatarPreview = document.getElementById("avatarPreview");
    const sidebarAvatar = document.getElementById("sidebarAvatar");

    if (avatarPreview) {
      avatarPreview.src = avatarUrl;
      // Add error handler to fallback to default if custom avatar fails to load
      avatarPreview.onerror = () => {
        if (avatarPreview.src !== ProfileModel.getDefaultAvatarUrl()) {
          avatarPreview.src = ProfileModel.getDefaultAvatarUrl();
        }
      };
    }

    if (sidebarAvatar) {
      sidebarAvatar.src = avatarUrl;
      // Add error handler to fallback to default if custom avatar fails to load
      sidebarAvatar.onerror = () => {
        if (sidebarAvatar.src !== ProfileModel.getDefaultAvatarUrl()) {
          sidebarAvatar.src = ProfileModel.getDefaultAvatarUrl();
        }
      };
    }

    // Update sidebar information
    const sidebarUsername = document.getElementById("sidebarUsername");
    const sidebarExperience = document.getElementById("sidebarExperience");

    if (sidebarUsername)
      sidebarUsername.textContent = username || "Username User";
    if (sidebarExperience)
      sidebarExperience.textContent = experience || "Experience Level";
  }

  showMessage(message, type = 'info') {
    // Create or update message element
    let messageElement = document.getElementById('profile-message');
    if (!messageElement) {
      messageElement = document.createElement('div');
      messageElement.id = 'profile-message';
      messageElement.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        max-width: 300px;
        word-wrap: break-word;
      `;
      document.body.appendChild(messageElement);
    }

    // Set message and style based on type
    messageElement.textContent = message;
    messageElement.style.backgroundColor = type === 'success' ? '#4CAF50' :
                                          type === 'error' ? '#f44336' : '#2196F3';
    messageElement.style.display = 'block';

    // Auto-hide after 3 seconds
    setTimeout(() => {
      if (messageElement) {
        messageElement.style.display = 'none';
      }
    }, 3000);
  }
}
